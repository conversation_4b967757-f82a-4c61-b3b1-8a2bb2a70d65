const RegionGroup = require("../models/RegionGroup");
const { isValidObjectId, default: mongoose } = require("mongoose");
const streamService = require("./Stream.service");

class RegionGroupService {
    async fetchAll() {
        const regionGroups = await RegionGroup.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "created_by",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: "$created_by",
            },
        ]);

        const vessels = await streamService.fetchAll();
        const updatedRegionGroups = regionGroups.map((regionGroup) => {
            const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup.unit_ids.includes(vessel.unit_id));
            return {
                ...regionGroup,
                vessels: vesselsInRegionGroup.map((vessel) => ({ unit_id: vessel.unit_id, name: vessel.name })),
            };
        });

        return updatedRegionGroups;
    }

    async findById({ id }) {
        if (!isValidObjectId(id)) throw new Error("Invalid region group id");
        const regionGroup = await RegionGroup.aggregate([
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(id),
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "created_by",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: "$created_by",
            },
        ]);

        if (!regionGroup.length) return null;

        const vessels = await streamService.fetchAll();
        const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup[0].unit_ids.includes(vessel.unit_id));

        return {
            ...regionGroup[0],
            vessels: vesselsInRegionGroup.map((vessel) => ({
                unit_id: vessel.unit_id,
                name: vessel.name,
            })),
        };
    }

    async create({ name, timezone, unit_ids, created_by }) {
        const regionGroup = await RegionGroup.create({ name, timezone, unit_ids, created_by });
        streamService.resetCache();
        return await this.findById({ id: regionGroup._id });
    }

    async update({ id, name, timezone, unit_ids }) {
        if (!isValidObjectId(id)) throw new Error("Invalid region group id");

        const data = { name, timezone, unit_ids };
        Object.keys(data).forEach((key) => {
            if (data[key] === undefined) delete data[key];
        });

        const regionGroup = await RegionGroup.findByIdAndUpdate(id, data, { new: true });
        streamService.resetCache();
        return await this.findById({ id: regionGroup._id });
    }

    async delete({ id }) {
        const result = await RegionGroup.findByIdAndDelete(id);
        streamService.resetCache();
        return result;
    }
}

const regionGroupService = new RegionGroupService();

module.exports = regionGroupService;
