const express = require("express");
const isAuthenticated = require("../middlewares/auth");
const { validateError } = require("../utils/functions");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const router = express.Router();
const streamService = require("../services/Stream.service");
const { validateData } = require("../middlewares/validator");
const { query } = require("express-validator");
// const { getLinkedUnits } = require("../../utils/linkedUnits");
const Vessel = require("../models/VesselManagement");
const RegionGroup = require("../models/RegionGroup");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

router.use("/", apiLimiter);

router.get(
    "/info",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSELS_INFO_V2),
    isAuthenticated,
    validateData.bind(this, [
        query("region_groups")
            .isString()
            .optional()
            .customSanitizer((v) => v.split(",").map((v) => v.trim())),
    ]),
    async (req, res) => {
        try {
            const { region_groups } = req.query;
            if (region_groups && region_groups === "") return res.status(400).json({ message: "Invalid region_groups" });

            // Get vessels from the vessels collection
            const vessels = await Vessel.find({});

            // Get region groups to filter by if specified
            let regionGroupsFilter = [];
            if (region_groups && region_groups.length > 0) {
                regionGroupsFilter = await RegionGroup.find({
                    _id: { $in: region_groups.map((id) => id) },
                });
            }

            // Get kinesis units (streams) - filter by regions if region_groups specified
            let regions = undefined;
            if (regionGroupsFilter.length > 0) {
                // Extract regions from the region groups
                const kinesisUnits = await streamService.fetchAll({});
                const regionGroupIds = regionGroupsFilter.map((rg) => rg._id.toString());
                const filteredUnits = kinesisUnits.filter((unit) => unit.region_group && regionGroupIds.includes(unit.region_group.toString()));
                regions = [...new Set(filteredUnits.map((unit) => unit.region))];
            }

            const kinesisUnits = await streamService.fetchAll({ regions });
            /* Commenting out linked units for now
            const accessibleVessels = vesselsList.filter((vessel) => canAccessUnit(req, vessel));

            accessibleVessels.forEach((vessel) => {
                const linkedUnits = getLinkedUnits(vessel.unit_id);

                if (linkedUnits.length === 1) return;

                linkedUnits.forEach((linkedUnit) => {
                    const linkedUnitInfo = vesselsList.find((v) => v.unit_id === linkedUnit);

                    if (!linkedUnitInfo) return;

                    linkedUnitInfo.linked_units = linkedUnits.map((luid) => {
                        const info = vesselsList.find((v) => v.unit_id === luid);
                        return {
                            unit_id: info.unit_id,
                            name: info.name,
                            thumbnail: info.thumbnail,
                            region: info.region,
                        };
                    });

                    const accesssibleUnitIndex = accessibleVessels.findIndex((v) => v.unit_id === linkedUnit);

                    if (accesssibleUnitIndex === -1) {
                        accessibleVessels.push(linkedUnitInfo);
                    } else {
                        accessibleVessels[accesssibleUnitIndex].linked_units = linkedUnitInfo.linked_units;
                    }
                });
            });
            */

            const kinesisMap = new Map();
            kinesisUnits.forEach((unit) => {
                kinesisMap.set(unit.unit_id, unit);
            });

            const vesselsList = vessels.map((vessel) => {
                const kinesisData = kinesisMap.get(vessel.unit_id);

                return {
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    name: vessel.name,
                    thumbnail: vessel?.thumbnail_s3_key || null,
                    region: kinesisData?.region || null,
                    is_live: kinesisData?.is_live || false,
                    timezone: kinesisData?.timezone || null,
                    region_group: kinesisData?.region_group || null,
                };
            });

            res.json(vesselsList);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
