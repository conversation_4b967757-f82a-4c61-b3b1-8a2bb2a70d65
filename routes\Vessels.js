const express = require("express");
const isAuthenticated = require("../middlewares/auth");
const { validateError } = require("../utils/functions");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const router = express.Router();
const streamService = require("../services/Stream.service");
const { validateData } = require("../middlewares/validator");
const { query } = require("express-validator");
// const { getLinkedUnits } = require("../utils/linkedUnits");
const Vessel = require("../models/VesselManagement");

const authUserApiLimiter = rateLimit({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

// Middleware to apply the correct limiter
function conditionalRateLimiter(req, res, next) {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    } else {
        apiLimiter(req, res, next);
    }
}

router.use("/", conditionalRateLimiter);

router.get(
    "/info",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSELS_INFO),
    isAuthenticated,
    validateData.bind(this, [
        query("regions")
            .isString()
            .optional()
            .customSanitizer((v) => v.split(",").map((v) => v.trim())),
    ]),
    async (req, res) => {
        try {
            const { regions } = req.query;

            const vessels = await Vessel.find({});
            const kinesisVessels = await streamService.fetchAll({ regions });
            /* Commenting out linked units for now
            const accessibleVessels = vesselsList.filter((vessel) => canAccessUnit(req, vessel));

            accessibleVessels.forEach((vessel) => {
                const linkedUnits = getLinkedUnits(vessel.unit_id);

                if (linkedUnits.length === 1) return;

                linkedUnits.forEach((linkedUnit) => {
                    const linkedUnitInfo = vesselsList.find((v) => v.unit_id === linkedUnit);

                    if (!linkedUnitInfo) return;

                    linkedUnitInfo.linked_units = linkedUnits.map((luid) => {
                        const info = vesselsList.find((v) => v.unit_id === luid);
                        return {
                            unit_id: info.unit_id,
                            name: info.name,
                            thumbnail: info.thumbnail,
                            region: info.region,
                        };
                    });

                    const accesssibleUnitIndex = accessibleVessels.findIndex((v) => v.unit_id === linkedUnit);

                    if (accesssibleUnitIndex === -1) {
                        accessibleVessels.push(linkedUnitInfo);
                    } else {
                        accessibleVessels[accesssibleUnitIndex].linked_units = linkedUnitInfo.linked_units;
                    }
                });
            });
            */

            const kinesisMap = new Map();
            kinesisVessels.forEach((vessel) => {
                kinesisMap.set(vessel.unit_id, vessel);
            });

            const vesselsList = vessels.map((vessel) => {
                const kinesisData = kinesisMap.get(vessel.unit_id);

                return {
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    name: vessel.name,
                    thumbnail: vessel?.thumbnail_s3_key || null,
                    region: kinesisData?.region || null,
                    is_live: kinesisData?.is_live || false,
                    timezone: kinesisData?.timezone || null,
                    region_group: kinesisData?.region_group || null,
                };
            });

            res.json(vesselsList);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Vessels
 *   description: Fetch vessel information
 */

/**
 * @swagger
 * /vessels/info:
 *   get:
 *     summary: Fetch list of vessels information
 *     description: Fetches a list of vessels with their respective details including the vessel ID, unit ID, name, and thumbnail image URL. Now fetches from vessels collection and populates is_live status from kinesis module.
 *     tags: [Vessels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: regions
 *         in: query
 *         required: false
 *         description: Comma-seperated AWS regions to fetch vessels for
 *         schema:
 *           type: string
 *           example: ap-southeast-1, us-east-1
 *     responses:
 *       200:
 *         description: An array of vessels information
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   vessel_id:
 *                     type: string
 *                     description: "The unique MongoDB ObjectId of the vessel."
 *                     example: "507f1f77bcf86cd799439011"
 *                   unit_id:
 *                     type: string
 *                     description: "The unique identifier of the unit."
 *                     example: "prototype-30"
 *                   name:
 *                     type: string
 *                     nullable: true
 *                     description: "The name of the vessel. Can be null if not provided."
 *                     example: "BRP Teresa Magbanua MRRV-9701"
 *                   thumbnail:
 *                     type: string
 *                     nullable: true
 *                     description: "The s3 key of the vessel's thumbnail image. Can be null if not provided."
 *                     example: "vessel-thumbnails/1748892241854-7crg7z9nk6i.jpg"
 *                   region:
 *                     type: string
 *                     nullable: true
 *                     description: "Region the vessel is listed in."
 *                     example: "ap-southeast-1"
 *                   is_live:
 *                     type: boolean
 *                     nullable: false
 *                     description: "Whether the camera is currently live."
 *                     example: true
 *                   timezone:
 *                     type: string
 *                     nullable: true
 *                     description: "Timezone of the vessel's region group."
 *                     example: "Asia/Manila"
 *                   region_group:
 *                     type: string
 *                     nullable: true
 *                     description: "MongoDB ObjectId of the region group."
 *                     example: "507f1f77bcf86cd799439012"
 *       401:
 *         description: Unauthorized, the user must be authenticated.
 *       500:
 *         description: "Internal server error"
 */
