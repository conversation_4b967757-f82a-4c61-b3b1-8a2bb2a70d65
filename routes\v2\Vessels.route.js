const express = require("express");
const isAuthenticated = require("../../middlewares/auth");
const { validateError } = require("../../utils/functions");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../../middlewares/assignEndpointId");
const { endpointIds } = require("../../utils/endpointIds");
const router = express.Router();
const streamService = require("../../services/Stream.service");
const { validateData } = require("../../middlewares/validator");
const { query } = require("express-validator");
// const { getLinkedUnits } = require("../../utils/linkedUnits");
const Vessel = require("../../models/VesselManagement");
const RegionGroup = require("../../models/RegionGroup");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

router.use("/", apiLimiter);

router.get(
    "/info",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSELS_INFO_V2),
    isAuthenticated,
    validateData.bind(this, [
        query("region_groups")
            .optional()
            .customSanitizer((v) => {
                if (!v || v.trim() === "") return [];
                return v.split(",").map((id) => id.trim()).filter(id => id.length > 0 && mongoose.Types.ObjectId.isValid(id));
            }),
    ]),
    async (req, res) => {
        try {
            const { region_groups } = req.query;

            // Helper function to map vessels with kinesis data
            const mapVesselsWithKinesisData = async (vessels) => {
                const kinesisUnits = await streamService.fetchAll({});
                const kinesisMap = new Map();
                kinesisUnits.forEach((unit) => {
                    kinesisMap.set(unit.unit_id, unit);
                });

                return vessels.map((vessel) => {
                    const kinesisData = kinesisMap.get(vessel.unit_id);
                    return {
                        vessel_id: vessel._id.toString(),
                        unit_id: vessel.unit_id,
                        name: vessel.name,
                        thumbnail: vessel?.thumbnail_s3_key || null,
                        region: kinesisData?.region || null,
                        is_live: kinesisData?.is_live || false,
                        timezone: kinesisData?.timezone || null,
                        region_group: kinesisData?.region_group || null,
                    };
                });
            };

            // Get all vessels from the database
            let vessels = await Vessel.find({});

            // Filter vessels by region_groups if specified
            if (region_groups && Array.isArray(region_groups) && region_groups.length > 0) {
                // Get region groups from database
                const regionGroupsFilter = await RegionGroup.find({
                    _id: { $in: region_groups }
                });

                // Extract vessel_ids from region groups
                const allowedVesselIds = regionGroupsFilter.reduce((acc, rg) => {
                    if (rg.vessel_ids && rg.vessel_ids.length > 0) {
                        acc.push(...rg.vessel_ids.map(id => id.toString()));
                    }
                    return acc;
                }, []);

                // Filter vessels to only include those in the region groups
                if (allowedVesselIds.length > 0) {
                    vessels = vessels.filter(vessel =>
                        allowedVesselIds.includes(vessel._id.toString())
                    );
                } else {
                    // No vessels found in the specified region groups
                    return res.json([]);
                }
            }

            // Map vessels with kinesis data and return
            const vesselsList = await mapVesselsWithKinesisData(vessels);
            res.json(vesselsList);

        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
