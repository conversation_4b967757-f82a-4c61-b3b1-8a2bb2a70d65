const express = require("express");
const isAuthenticated = require("../../middlewares/auth");
const { validateError } = require("../../utils/functions");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../../middlewares/assignEndpointId");
const { endpointIds } = require("../../utils/endpointIds");
const router = express.Router();
const streamService = require("../../services/Stream.service");
const { validateData } = require("../../middlewares/validator");
const { query } = require("express-validator");
// const { getLinkedUnits } = require("../../utils/linkedUnits");
const Vessel = require("../../models/VesselManagement");
const RegionGroup = require("../../models/RegionGroup");
const { default: mongoose } = require("mongoose");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

router.use("/", apiLimiter);

router.get(
    "/info",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSELS_INFO_V2),
    isAuthenticated,
    validateData.bind(this, [
        query("region_groups")
            .optional()
            .customSanitizer((v) => {
                if (!v || v.trim() === "") return [];
                return v
                    .split(",")
                    .map((id) => id.trim())
                    .filter((id) => id.length > 0 && mongoose.Types.ObjectId.isValid(id));
            }),
    ]),
    async (req, res) => {
        try {
            const { region_groups } = req.query;
            let vessels;

            if (region_groups && Array.isArray(region_groups) && region_groups.length > 0) {
                const regionGroupsData = await RegionGroup.find({
                    _id: { $in: region_groups },
                });

                const allowedVesselIds = regionGroupsData.reduce((acc, rg) => {
                    if (rg.vessel_ids && rg.vessel_ids.length > 0) {
                        acc.push(...rg.vessel_ids);
                    }
                    return acc;
                }, []);

                if (allowedVesselIds.length > 0) {
                    vessels = await Vessel.find({
                        _id: { $in: allowedVesselIds },
                    });
                } else {
                    return res.json([]);
                }
            } else {
                vessels = await Vessel.find({});
            }

            const kinesisUnits = await streamService.fetchAll({});
            /* Commenting out linked units for now
            const accessibleVessels = vesselsList.filter((vessel) => canAccessUnit(req, vessel));

            accessibleVessels.forEach((vessel) => {
                const linkedUnits = getLinkedUnits(vessel.unit_id);

                if (linkedUnits.length === 1) return;

                linkedUnits.forEach((linkedUnit) => {
                    const linkedUnitInfo = vesselsList.find((v) => v.unit_id === linkedUnit);

                    if (!linkedUnitInfo) return;

                    linkedUnitInfo.linked_units = linkedUnits.map((luid) => {
                        const info = vesselsList.find((v) => v.unit_id === luid);
                        return {
                            unit_id: info.unit_id,
                            name: info.name,
                            thumbnail: info.thumbnail,
                            region: info.region,
                        };
                    });

                    const accesssibleUnitIndex = accessibleVessels.findIndex((v) => v.unit_id === linkedUnit);

                    if (accesssibleUnitIndex === -1) {
                        accessibleVessels.push(linkedUnitInfo);
                    } else {
                        accessibleVessels[accesssibleUnitIndex].linked_units = linkedUnitInfo.linked_units;
                    }
                });
            });
            */

            const kinesisMap = new Map();
            kinesisUnits.forEach((unit) => {
                kinesisMap.set(unit.unit_id, unit);
            });

            const vesselsList = vessels.map((vessel) => {
                const kinesisData = kinesisMap.get(vessel.unit_id);

                return {
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    name: vessel.name,
                    thumbnail: vessel?.thumbnail_s3_key || null,
                    region: kinesisData?.region || null,
                    is_live: kinesisData?.is_live || false,
                    timezone: kinesisData?.timezone || null,
                    region_group: kinesisData?.region_group || null,
                };
            });

            res.json(vesselsList);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
