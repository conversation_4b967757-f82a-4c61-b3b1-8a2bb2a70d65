import axiosInstance from "../axios";

class VesselManagementController {
    async fetchAll({ page = 1, limit = 10, search = "" }) {
        const response = await axiosInstance.get("/vesselManagement", { params: { page, limit, search } });
        return response.data;
    }

    async fetchAllVessels() {
        const response = await axiosInstance.get("/vesselManagement", { params: { page: 1, limit: 1000, search: "" } });
        return response.data.vessels || [];
    }

    async create({ name, thumbnail_file, unit_id, is_active = true }) {
        const formData = new FormData();
        formData.append("name", name);
        if (thumbnail_file) formData.append("thumbnail_file", thumbnail_file);
        if (unit_id) formData.append("unit_id", unit_id);
        formData.append("is_active", is_active);

        const response = await axiosInstance.post("/vesselManagement", formData, {
            headers: { "Content-Type": "multipart/form-data" },
            meta: { showSnackbar: true },
        });
        return response.data;
    }

    async update({ id, name, thumbnail_file, unit_id, is_active, remove_thumbnail }) {
        const formData = new FormData();
        formData.append("name", name);
        if (thumbnail_file) formData.append("thumbnail_file", thumbnail_file);
        if (unit_id !== undefined) formData.append("unit_id", unit_id);
        formData.append("is_active", is_active);
        if (remove_thumbnail) formData.append("remove_thumbnail", remove_thumbnail);

        const response = await axiosInstance.post(`/vesselManagement/${id}`, formData, {
            headers: { "Content-Type": "multipart/form-data" },
            meta: { showSnackbar: true },
        });
        return response.data;
    }

    async fetchById({ id }) {
        const response = await axiosInstance.get(`/vesselManagement/${id}`);
        return response.data;
    }

    async fetchUnitIds({ regions }) {
        const response = await axiosInstance.get("/vesselManagement/unitIds", { params: { regions } });
        return response.data;
    }

    async fetchAssignedUnitIds() {
        const response = await axiosInstance.get("/vesselManagement/assignedUnitIds");
        return response.data;
    }
}

const vesselManagementController = new VesselManagementController();

export default vesselManagementController;
