const express = require("express");
const hasPermission = require("../middlewares/hasPermission");
const { permissions } = require("../utils/permissions");
const { validateData } = require("../middlewares/validator");
const { body, param } = require("express-validator");
const { validateError } = require("../utils/functions");
const { default: mongoose } = require("mongoose");
const { isValidObjectId } = require("mongoose");
const { isValidTimezoneOffset } = require("../utils/timezonesList");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const regionGroupService = require("../services/RegionGroup.service");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_REGION_GROUPS), isAuthenticated, async (req, res) => {
    try {
        const regionGroups = await regionGroupService.fetchAll();
        res.json(regionGroups);
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        body("name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("timezone")
            .isString()
            .notEmpty()
            .custom(isValidTimezoneOffset)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("unit_ids")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("unit_ids.*")
            .isString()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("vessel_ids")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("vessel_ids.*")
            .isString()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
    ]),
    async (req, res) => {
        try {
            const { name, timezone, unit_ids, vessel_ids } = req.body;

            const regionGroup = await regionGroupService.create({ name, timezone, unit_ids, vessel_ids, created_by: req.user._id });
            res.json({ message: `Region group has been created`, regionGroup });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("timezone")
            .isString()
            .notEmpty()
            .custom(isValidTimezoneOffset)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("unit_ids")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("unit_ids.*")
            .isString()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("vessel_ids")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("vessel_ids.*")
            .isString()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
    ]),
    async (req, res) => {
        try {
            const data = req.body;

            const result = await regionGroupService.update({ id: req.params.id, ...data });

            if (!result) return res.status(400).json({ message: `Region group does not exist` });

            return res.json({ message: `Region group '${data.name}' has been edited`, regionGroup: result });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { id } = req.params;

            const result = await regionGroupService.delete({ id });
            if (!result) return res.status(400).json({ message: `Region group does not exist` });

            return res.json({ message: `Region group has been deleted` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
